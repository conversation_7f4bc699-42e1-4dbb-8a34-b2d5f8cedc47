{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_452e0654.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "J5zCLlcMqT0taO0fnMmR8pb4QqXI86+7Fi41gsD6KfM=", "__NEXT_PREVIEW_MODE_ID": "1b7ef3d903e11f5f9d8f9779d147a10a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9a88abf288ee129a6379874b86da133dfcfad6aeec4b076879a2aac1d13a73c9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c013b1882573c2c9aa2e9bb4360d52ee985b73e93015375b4d3fe5bdc5f7970f"}}}, "instrumentation": null, "functions": {}}