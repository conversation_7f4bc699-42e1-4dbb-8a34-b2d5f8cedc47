{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport {\n  ArrowRight,\n  Palette,\n  FileText,\n  Zap,\n  Star,\n  Users,\n  Shield,\n  Smartphone,\n  Globe,\n  Code,\n  Layers,\n  Rocket,\n  Heart,\n  CheckCircle,\n  TrendingUp,\n  Award,\n  Clock,\n  Mail,\n  Phone,\n  MapPin,\n  Facebook,\n  Twitter,\n  Instagram,\n  Linkedin,\n  Github,\n  Eye,\n  ShoppingCart,\n  Search,\n  Filter,\n  Grid,\n  List,\n  Bookmark,\n  Share2,\n  MessageCircle,\n  ThumbsUp,\n  Lightbulb,\n  Infinity,\n  Cpu,\n  Database,\n  Cloud,\n  Lock,\n  Gauge,\n  Headphones,\n  Play,\n  ExternalLink,\n  Download,\n  ChevronRight,\n  Sparkles,\n  Target,\n  BarChart3\n} from \"lucide-react\"\nimport Link from \"next/link\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { Database } from \"@/lib/database.types\"\n\ntype Template = Database['public']['Tables']['templates']['Row']\n\nexport default function Home() {\n  const [featuredTemplates, setFeaturedTemplates] = useState<Template[]>([])\n  const [stats, setStats] = useState({\n    templates: 50,\n    users: 10000,\n    downloads: 25000,\n    rating: 4.9\n  })\n  const [loading, setLoading] = useState(true)\n  const [email, setEmail] = useState(\"\")\n\n  useEffect(() => {\n    loadFeaturedTemplates()\n    loadStats()\n  }, [])\n\n  const loadFeaturedTemplates = async () => {\n    try {\n      const supabase = createClient()\n\n      // Load all templates for now (we'll filter featured ones later)\n      console.log('Loading templates...')\n      const { data, error } = await supabase\n        .from('templates')\n        .select('*')\n        .limit(6)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Templates error:', error)\n        throw error\n      }\n\n      console.log('Templates loaded:', data?.length || 0, data)\n      setFeaturedTemplates(data || [])\n    } catch (error) {\n      console.error('Error loading featured templates:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadStats = async () => {\n    try {\n      const supabase = createClient()\n\n      // Get template count\n      const { count: templateCount } = await supabase\n        .from('templates')\n        .select('*', { count: 'exact', head: true })\n        .eq('is_active', true)\n\n      // Get user count\n      const { count: userCount } = await supabase\n        .from('profiles')\n        .select('*', { count: 'exact', head: true })\n\n      // Get download count (sum of all template downloads_count)\n      const { data: downloadData } = await supabase\n        .from('templates')\n        .select('downloads_count')\n        .eq('is_active', true)\n\n      const totalDownloads = downloadData?.reduce((sum, template) => sum + (template.downloads_count || 0), 0) || 0\n\n      setStats({\n        templates: templateCount || 50,\n        users: userCount || 10000,\n        downloads: totalDownloads || 25000,\n        rating: 4.9\n      })\n    } catch (error) {\n      console.error('Error loading stats:', error)\n    }\n  }\n\n  const handleNewsletterSignup = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!email) return\n\n    try {\n      const supabase = createClient()\n      const { error } = await supabase\n        .from('email_subscriptions')\n        .insert({\n          email,\n          source: 'homepage_newsletter'\n        })\n\n      if (error) throw error\n\n      setEmail(\"\")\n      // Show success message (you can add toast notification here)\n      alert(\"Thank you for subscribing to our newsletter!\")\n    } catch (error) {\n      console.error('Newsletter signup error:', error)\n      alert(\"Error subscribing. Please try again.\")\n    }\n  }\n\n  return (\n    <div className=\"space-y-16\">\n      {/* Enhanced Hero Section */}\n      <div className=\"relative overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 -z-10\" />\n        <div className=\"absolute top-0 left-1/4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\" />\n        <div className=\"absolute top-0 right-1/4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000\" />\n\n        <div className=\"text-center space-y-8 py-20\">\n          <div className=\"space-y-6\">\n            <Badge variant=\"secondary\" className=\"text-sm px-6 py-3 bg-gradient-to-r from-blue-100 to-purple-100 border-0 hover:scale-105 transition-transform\">\n              <Sparkles className=\"h-4 w-4 mr-2 text-blue-600\" />\n              New: {stats.templates}+ Premium Templates Available\n            </Badge>\n\n            <h1 className=\"text-6xl md:text-7xl lg:text-8xl font-bold tracking-tight\">\n              <span className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent\">\n                Welcome to\n              </span>\n              <br />\n              <span className=\"bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent\">\n                KaleidoneX\n              </span>\n            </h1>\n\n            <p className=\"text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed\">\n              Create stunning, customizable templates with our powerful design tools.\n              Build beautiful websites and applications with ease using our advanced customization engine.\n            </p>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\n            <Link href=\"/templates\">\n              <Button size=\"lg\" className=\"text-lg px-10 py-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                Browse Templates\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Button>\n            </Link>\n            <Link href=\"/customize\">\n              <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-10 py-6 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:scale-105\">\n                <Palette className=\"mr-2 h-5 w-5\" />\n                Start Customizing\n              </Button>\n            </Link>\n            <Link href=\"#demo\">\n              <Button variant=\"ghost\" size=\"lg\" className=\"text-lg px-6 py-6 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300\">\n                <Play className=\"mr-2 h-5 w-5\" />\n                Watch Demo\n              </Button>\n            </Link>\n          </div>\n\n          {/* Enhanced Trust Indicators */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 pt-12 max-w-2xl mx-auto\">\n            <div className=\"flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform\">\n              <Star className=\"h-6 w-6 text-yellow-500 fill-current\" />\n              <span className=\"font-semibold text-lg\">{stats.rating}/5</span>\n              <span className=\"text-sm text-muted-foreground\">Rating</span>\n            </div>\n            <div className=\"flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform\">\n              <Users className=\"h-6 w-6 text-blue-500\" />\n              <span className=\"font-semibold text-lg\">{(stats.users / 1000).toFixed(0)}K+</span>\n              <span className=\"text-sm text-muted-foreground\">Users</span>\n            </div>\n            <div className=\"flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform\">\n              <Download className=\"h-6 w-6 text-green-500\" />\n              <span className=\"font-semibold text-lg\">{(stats.downloads / 1000).toFixed(0)}K+</span>\n              <span className=\"text-sm text-muted-foreground\">Downloads</span>\n            </div>\n            <div className=\"flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform\">\n              <Shield className=\"h-6 w-6 text-purple-500\" />\n              <span className=\"font-semibold text-lg\">100%</span>\n              <span className=\"text-sm text-muted-foreground\">Secure</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Featured Templates Section */}\n      <div className=\"space-y-8\">\n        <div className=\"text-center space-y-4\">\n          <Badge variant=\"outline\" className=\"px-4 py-2\">\n            <Award className=\"h-4 w-4 mr-2\" />\n            Featured Templates\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n            Popular Templates\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Discover our most popular and highly-rated templates, loved by thousands of users worldwide\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n            {[...Array(6)].map((_, i) => (\n              <Card key={i} className=\"overflow-hidden animate-pulse\">\n                <div className=\"aspect-video bg-gray-200\" />\n                <CardContent className=\"p-6\">\n                  <div className=\"space-y-3\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n                    <div className=\"h-3 bg-gray-200 rounded w-full\" />\n                    <div className=\"h-3 bg-gray-200 rounded w-2/3\" />\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : (\n          <div className=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3\">\n            {featuredTemplates.map((template) => (\n              <Card key={template.id} className=\"group overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg hover:scale-105\">\n                {/* Image Container with Overlay */}\n                <div className=\"aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden\">\n                  {template.preview_image ? (\n                    <img\n                      src={template.preview_image}\n                      alt={template.title}\n                      className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                    />\n                  ) : (\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <div className=\"text-center space-y-2\">\n                        <FileText className=\"h-12 w-12 text-blue-400 mx-auto\" />\n                        <p className=\"text-sm text-muted-foreground\">Template Preview</p>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Overlay with Actions */}\n                  <div className=\"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-3\">\n                    <Button size=\"sm\" variant=\"secondary\" className=\"bg-white/90 hover:bg-white\">\n                      <Eye className=\"h-4 w-4 mr-2\" />\n                      Preview\n                    </Button>\n                    <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700\">\n                      <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                      Buy Now\n                    </Button>\n                  </div>\n\n                  {/* Price Badge */}\n                  <div className=\"absolute top-4 right-4\">\n                    {template.original_price && template.original_price > template.price ? (\n                      <div className=\"flex flex-col items-end gap-1\">\n                        <Badge className=\"bg-red-500 text-white hover:bg-red-600\">\n                          {template.discount_percentage}% OFF\n                        </Badge>\n                        <div className=\"bg-white/90 rounded px-2 py-1\">\n                          <div className=\"text-xs text-gray-500 line-through\">₹{template.original_price}</div>\n                          <div className=\"text-sm font-semibold text-gray-900\">₹{template.price}</div>\n                        </div>\n                      </div>\n                    ) : template.is_free ? (\n                      <Badge className=\"bg-green-500 text-white hover:bg-green-600\">\n                        FREE\n                      </Badge>\n                    ) : (\n                      <Badge className=\"bg-white/90 text-gray-900 hover:bg-white\">\n                        ₹{template.price}\n                      </Badge>\n                    )}\n                  </div>\n\n                  {/* Featured Badge */}\n                  {template.is_featured && (\n                    <div className=\"absolute top-4 left-4\">\n                      <Badge className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0\">\n                        <Star className=\"h-3 w-3 mr-1\" />\n                        Featured\n                      </Badge>\n                    </div>\n                  )}\n                </div>\n\n                <CardContent className=\"p-6\">\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h3 className=\"text-xl font-semibold group-hover:text-blue-600 transition-colors\">\n                        {template.title}\n                      </h3>\n                      <p className=\"text-sm text-muted-foreground mt-2 line-clamp-2\">\n                        {template.description}\n                      </p>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                        <div className=\"flex items-center gap-1\">\n                          <Eye className=\"h-4 w-4\" />\n                          <span>{template.views_count || 0}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Download className=\"h-4 w-4\" />\n                          <span>{template.downloads_count || 0}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />\n                          <span>{template.rating || 0}</span>\n                        </div>\n                      </div>\n                      <Button variant=\"ghost\" size=\"sm\" className=\"p-2\">\n                        <Bookmark className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n\n        <div className=\"text-center\">\n          <Link href=\"/templates\">\n            <Button size=\"lg\" variant=\"outline\" className=\"px-8 py-3\">\n              View All Templates\n              <ChevronRight className=\"ml-2 h-5 w-5\" />\n            </Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Enhanced Features Grid */}\n      <div className=\"space-y-8\">\n        <div className=\"text-center space-y-4\">\n          <h2 className=\"text-3xl font-bold\">Why Choose KaleidoneX?</h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Discover the powerful features that make KaleidoneX the best choice for your template needs\n          </p>\n        </div>\n\n        <div className=\"grid gap-6 md:grid-cols-3\">\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <FileText className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <CardTitle>50+ Premium Templates</CardTitle>\n              </div>\n              <CardDescription>\n                Choose from a wide variety of professionally designed templates\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                Our template library includes designs for every industry and use case.\n                From business websites to creative portfolios, find the perfect starting point.\n              </p>\n              <div className=\"flex flex-wrap gap-2\">\n                <Badge variant=\"outline\">Business</Badge>\n                <Badge variant=\"outline\">Portfolio</Badge>\n                <Badge variant=\"outline\">E-commerce</Badge>\n                <Badge variant=\"outline\">Blog</Badge>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <Palette className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <CardTitle>Live Customization</CardTitle>\n              </div>\n              <CardDescription>\n                Customize your templates in real-time with our visual editor\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                See your changes instantly as you customize colors, fonts, layouts, and content.\n                No coding required - just point, click, and create.\n              </p>\n              <div className=\"flex items-center gap-2 text-sm text-green-600\">\n                <CheckCircle className=\"h-4 w-4\" />\n                <span>Real-time preview</span>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <Zap className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <CardTitle>Fast & Modern</CardTitle>\n              </div>\n              <CardDescription>\n                Built with the latest technologies for optimal performance\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                Powered by Next.js, Tailwind CSS, and modern web standards.\n                Your templates will be fast, responsive, and SEO-friendly.\n              </p>\n              <div className=\"flex items-center gap-2 text-sm text-blue-600\">\n                <TrendingUp className=\"h-4 w-4\" />\n                <span>99.9% Uptime</span>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"p-2 bg-orange-100 rounded-lg\">\n                  <Smartphone className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <CardTitle>Mobile Responsive</CardTitle>\n              </div>\n              <CardDescription>\n                All templates are fully responsive and mobile-optimized\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                Your templates will look perfect on all devices - desktop, tablet, and mobile.\n                Automatic responsive design ensures great user experience everywhere.\n              </p>\n              <div className=\"flex items-center gap-2 text-sm text-orange-600\">\n                <Globe className=\"h-4 w-4\" />\n                <span>Cross-platform</span>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"p-2 bg-red-100 rounded-lg\">\n                  <Code className=\"h-6 w-6 text-red-600\" />\n                </div>\n                <CardTitle>No Code Required</CardTitle>\n              </div>\n              <CardDescription>\n                Create professional websites without writing a single line of code\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                Our intuitive drag-and-drop interface makes it easy for anyone to create\n                stunning websites, regardless of technical expertise.\n              </p>\n              <div className=\"flex items-center gap-2 text-sm text-red-600\">\n                <Heart className=\"h-4 w-4\" />\n                <span>User-friendly</span>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-center gap-2\">\n                <div className=\"p-2 bg-indigo-100 rounded-lg\">\n                  <Layers className=\"h-6 w-6 text-indigo-600\" />\n                </div>\n                <CardTitle>Advanced Features</CardTitle>\n              </div>\n              <CardDescription>\n                Powerful features for professional websites and applications\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                Payment integration, user authentication, admin panels, analytics,\n                and much more. Everything you need for a complete solution.\n              </p>\n              <div className=\"flex items-center gap-2 text-sm text-indigo-600\">\n                <Award className=\"h-4 w-4\" />\n                <span>Enterprise-ready</span>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Stats Section */}\n      <div className=\"space-y-8\">\n        <div className=\"text-center space-y-4\">\n          <h2 className=\"text-3xl font-bold\">Trusted by Thousands</h2>\n          <p className=\"text-lg text-muted-foreground\">\n            Join the growing community of creators using KaleidoneX\n          </p>\n        </div>\n\n        <div className=\"grid gap-6 md:grid-cols-4\">\n          <Card className=\"text-center hover:shadow-lg transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-2\">\n                <div className=\"text-4xl font-bold text-blue-600\">50+</div>\n                <p className=\"text-sm font-medium\">Premium Templates</p>\n                <p className=\"text-xs text-muted-foreground\">Professionally designed</p>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"text-center hover:shadow-lg transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-2\">\n                <div className=\"text-4xl font-bold text-green-600\">10K+</div>\n                <p className=\"text-sm font-medium\">Happy Users</p>\n                <p className=\"text-xs text-muted-foreground\">Worldwide community</p>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"text-center hover:shadow-lg transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-2\">\n                <div className=\"text-4xl font-bold text-purple-600\">24/7</div>\n                <p className=\"text-sm font-medium\">Support</p>\n                <p className=\"text-xs text-muted-foreground\">Always here to help</p>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"text-center hover:shadow-lg transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-2\">\n                <div className=\"text-4xl font-bold text-orange-600\">99.9%</div>\n                <p className=\"text-sm font-medium\">Uptime</p>\n                <p className=\"text-xs text-muted-foreground\">Reliable service</p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Testimonials Section */}\n      <div className=\"space-y-8\">\n        <div className=\"text-center space-y-4\">\n          <h2 className=\"text-3xl font-bold\">What Our Users Say</h2>\n          <p className=\"text-lg text-muted-foreground\">\n            Don't just take our word for it - hear from our satisfied customers\n          </p>\n        </div>\n\n        <div className=\"grid gap-6 md:grid-cols-3\">\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center gap-1\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"h-4 w-4 text-yellow-500 fill-current\" />\n                  ))}\n                </div>\n                <p className=\"text-sm text-muted-foreground\">\n                  \"KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!\"\n                </p>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                    <Users className=\"h-5 w-5 text-blue-600\" />\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium\">Sarah Johnson</p>\n                    <p className=\"text-xs text-muted-foreground\">Small Business Owner</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center gap-1\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"h-4 w-4 text-yellow-500 fill-current\" />\n                  ))}\n                </div>\n                <p className=\"text-sm text-muted-foreground\">\n                  \"As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!\"\n                </p>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center\">\n                    <Palette className=\"h-5 w-5 text-purple-600\" />\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium\">Mike Chen</p>\n                    <p className=\"text-xs text-muted-foreground\">Web Designer</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center gap-1\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"h-4 w-4 text-yellow-500 fill-current\" />\n                  ))}\n                </div>\n                <p className=\"text-sm text-muted-foreground\">\n                  \"The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!\"\n                </p>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                    <Heart className=\"h-5 w-5 text-green-600\" />\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium\">Emily Davis</p>\n                    <p className=\"text-xs text-muted-foreground\">Freelancer</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"text-center space-y-6 py-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl\">\n        <div className=\"space-y-4\">\n          <h2 className=\"text-3xl font-bold\">Ready to Get Started?</h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Join thousands of creators who are already building amazing websites with KaleidoneX.\n            Start your journey today!\n          </p>\n        </div>\n\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n          <Link href=\"/templates\">\n            <Button size=\"lg\" className=\"text-lg px-8 py-6\">\n              <Rocket className=\"mr-2 h-5 w-5\" />\n              Get Started Now\n            </Button>\n          </Link>\n          <Link href=\"/contact\">\n            <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-6\">\n              <Clock className=\"mr-2 h-5 w-5\" />\n              Schedule Demo\n            </Button>\n          </Link>\n        </div>\n\n        <p className=\"text-sm text-muted-foreground\">\n          No credit card required • 14-day free trial • Cancel anytime\n        </p>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-gradient-to-r from-slate-900 to-slate-800 text-white rounded-2xl mt-16\">\n        <div className=\"px-8 py-12\">\n          {/* Main Footer Content */}\n          <div className=\"grid gap-8 md:grid-cols-2 lg:grid-cols-4\">\n            {/* Company Info */}\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <h3 className=\"text-2xl font-bold\">KaleidoneX</h3>\n                <p className=\"text-slate-300 text-sm leading-relaxed\">\n                  Creating stunning, customizable templates for modern web applications.\n                  Build beautiful websites with our powerful design tools.\n                </p>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex gap-3\">\n                <Button size=\"sm\" variant=\"ghost\" className=\"w-10 h-10 p-0 hover:bg-white/10\">\n                  <Facebook className=\"h-4 w-4\" />\n                </Button>\n                <Button size=\"sm\" variant=\"ghost\" className=\"w-10 h-10 p-0 hover:bg-white/10\">\n                  <Twitter className=\"h-4 w-4\" />\n                </Button>\n                <Button size=\"sm\" variant=\"ghost\" className=\"w-10 h-10 p-0 hover:bg-white/10\">\n                  <Instagram className=\"h-4 w-4\" />\n                </Button>\n                <Button size=\"sm\" variant=\"ghost\" className=\"w-10 h-10 p-0 hover:bg-white/10\">\n                  <Linkedin className=\"h-4 w-4\" />\n                </Button>\n                <Button size=\"sm\" variant=\"ghost\" className=\"w-10 h-10 p-0 hover:bg-white/10\">\n                  <Github className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n\n            {/* Quick Links */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold\">Quick Links</h4>\n              <div className=\"space-y-2\">\n                <Link href=\"/templates\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  Browse Templates\n                </Link>\n                <Link href=\"/customize\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  Customize Templates\n                </Link>\n                <Link href=\"/contact\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  Contact Us\n                </Link>\n                <Link href=\"/login\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  Sign In\n                </Link>\n              </div>\n            </div>\n\n            {/* Categories */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold\">Categories</h4>\n              <div className=\"space-y-2\">\n                <Link href=\"/templates?category=Business\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  Business Templates\n                </Link>\n                <Link href=\"/templates?category=Portfolio\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  Portfolio Templates\n                </Link>\n                <Link href=\"/templates?category=E-commerce\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  E-commerce Templates\n                </Link>\n                <Link href=\"/templates?category=Blog\" className=\"block text-slate-300 hover:text-white transition-colors text-sm\">\n                  Blog Templates\n                </Link>\n              </div>\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold\">Get in Touch</h4>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center gap-3 text-sm\">\n                  <Mail className=\"h-4 w-4 text-slate-400\" />\n                  <span className=\"text-slate-300\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center gap-3 text-sm\">\n                  <Phone className=\"h-4 w-4 text-slate-400\" />\n                  <span className=\"text-slate-300\">+****************</span>\n                </div>\n                <div className=\"flex items-center gap-3 text-sm\">\n                  <MapPin className=\"h-4 w-4 text-slate-400\" />\n                  <span className=\"text-slate-300\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Newsletter */}\n              <div className=\"space-y-2\">\n                <h5 className=\"text-sm font-medium\">Stay Updated</h5>\n                <div className=\"flex gap-2\">\n                  <input\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"flex-1 px-3 py-2 text-sm bg-white/10 border border-white/20 rounded-md text-white placeholder:text-slate-400 focus:outline-none focus:ring-2 focus:ring-white/30\"\n                  />\n                  <Button size=\"sm\" variant=\"secondary\">\n                    Subscribe\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Footer Bottom */}\n          <div className=\"border-t border-white/10 mt-8 pt-8\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n              <div className=\"text-sm text-slate-400\">\n                © 2024 KaleidoneX. All rights reserved.\n              </div>\n\n              <div className=\"flex gap-6 text-sm\">\n                <Link href=\"/privacy\" className=\"text-slate-400 hover:text-white transition-colors\">\n                  Privacy Policy\n                </Link>\n                <Link href=\"/terms\" className=\"text-slate-400 hover:text-white transition-colors\">\n                  Terms of Service\n                </Link>\n                <Link href=\"/cookies\" className=\"text-slate-400 hover:text-white transition-colors\">\n                  Cookie Policy\n                </Link>\n              </div>\n\n              <div className=\"flex items-center gap-2 text-sm text-slate-400\">\n                <Heart className=\"h-4 w-4 text-red-400\" />\n                <span>Made with love</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoDA;AACA;;;AA5DA;;;;;;;;AAiEe,SAAS;;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,WAAW;QACX,OAAO;QACP,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;YACA;QACF;yBAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAE5B,gEAAgE;YAChE,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,GACN,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,qBAAqB,MAAM,UAAU,GAAG;YACpD,qBAAqB,QAAQ,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAE5B,qBAAqB;YACrB,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,aACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,aAAa;YAEnB,iBAAiB;YACjB,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,YACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAE5C,2DAA2D;YAC3D,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,aACL,MAAM,CAAC,mBACP,EAAE,CAAC,aAAa;YAEnB,MAAM,iBAAiB,cAAc,OAAO,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,eAAe,IAAI,CAAC,GAAG,MAAM;YAE5G,SAAS;gBACP,WAAW,iBAAiB;gBAC5B,OAAO,aAAa;gBACpB,WAAW,kBAAkB;gBAC7B,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,EAAE,cAAc;QAChB,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC;gBACN;gBACA,QAAQ;YACV;YAEF,IAAI,OAAO,MAAM;YAEjB,SAAS;YACT,6DAA6D;YAC7D,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA+B;4CAC7C,MAAM,SAAS;4CAAC;;;;;;;kDAGxB,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;0DAA0F;;;;;;0DAG1G,6LAAC;;;;;0DACD,6LAAC;gDAAK,WAAU;0DAAyF;;;;;;;;;;;;kDAK3G,6LAAC;wCAAE,WAAU;kDAA8E;;;;;;;;;;;;0CAM7F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;;gDAAgL;8DAE1M,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;;8DAC5C,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIxC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAOvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;;oDAAyB,MAAM,MAAM;oDAAC;;;;;;;0DACtD,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;;oDAAyB,CAAC,MAAM,KAAK,GAAG,IAAI,EAAE,OAAO,CAAC;oDAAG;;;;;;;0DACzE,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;;oDAAyB,CAAC,MAAM,SAAS,GAAG,IAAI,EAAE,OAAO,CAAC;oDAAG;;;;;;;0DAC7E,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,6LAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;oBAKhE,wBACC,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAS,WAAU;;kDACtB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BANV;;;;;;;;;6CAaf,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC,mIAAA,CAAA,OAAI;gCAAmB,WAAU;;kDAEhC,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,aAAa,iBACrB,6LAAC;gDACC,KAAK,SAAS,aAAa;gDAC3B,KAAK,SAAS,KAAK;gDACnB,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;0DAMnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAY,WAAU;;0EAC9C,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGlC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,WAAU;;0EAC1B,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAM7C,6LAAC;gDAAI,WAAU;0DACZ,SAAS,cAAc,IAAI,SAAS,cAAc,GAAG,SAAS,KAAK,iBAClE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;;gEACd,SAAS,mBAAmB;gEAAC;;;;;;;sEAEhC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAqC;wEAAE,SAAS,cAAc;;;;;;;8EAC7E,6LAAC;oEAAI,WAAU;;wEAAsC;wEAAE,SAAS,KAAK;;;;;;;;;;;;;;;;;;2DAGvE,SAAS,OAAO,iBAClB,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAA6C;;;;;yEAI9D,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;;wDAA2C;wDACxD,SAAS,KAAK;;;;;;;;;;;;4CAMrB,SAAS,WAAW,kBACnB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;kDAOzC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,SAAS,KAAK;;;;;;sEAEjB,6LAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;;;;;;;8DAIzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;sFACf,6LAAC;sFAAM,SAAS,WAAW,IAAI;;;;;;;;;;;;8EAEjC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;sFAAM,SAAS,eAAe,IAAI;;;;;;;;;;;;8EAErC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;sFAAM,SAAS,MAAM,IAAI;;;;;;;;;;;;;;;;;;sEAG9B,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAK,WAAU;sEAC1C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA3FnB,SAAS,EAAE;;;;;;;;;;kCAqG5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAU,WAAU;;oCAAY;kDAExD,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAKjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAK/B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;0DAEb,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAK/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAK/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGf,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAI7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOvD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGf,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAI7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOvD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGf,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAI7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;;sDAC1B,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIvC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAMxC,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAM/C,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;sDAOxD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAQ,WAAU;8DAC1C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAQ,WAAU;8DAC1C,cAAA,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAQ,WAAU;8DAC1C,cAAA,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAQ,WAAU;8DAC1C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAQ,WAAU;8DAC1C,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;8DAAkE;;;;;;8DAGpG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;8DAAkE;;;;;;8DAGpG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAkE;;;;;;8DAGlG,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAkE;;;;;;;;;;;;;;;;;;8CAOpG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA+B,WAAU;8DAAkE;;;;;;8DAGtH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgC,WAAU;8DAAkE;;;;;;8DAGvH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiC,WAAU;8DAAkE;;;;;;8DAGxH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA2B,WAAU;8DAAkE;;;;;;;;;;;;;;;;;;8CAOtH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;sDAKrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS9C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;kDAIxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoD;;;;;;0DAGpF,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAoD;;;;;;0DAGlF,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoD;;;;;;;;;;;;kDAKtF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GAnxBwB;KAAA", "debugId": null}}]}