-- =====================================================
-- ADMIN PANEL DELETE FUNCTIONALITY FIX
-- =====================================================
-- Run this in Supabase SQL Editor to fix delete issues

-- =====================================================
-- 1. FIX RLS POLICIES FOR ADMIN DELETE OPERATIONS
-- =====================================================

-- Drop existing policies that might be blocking admin operations
DROP POLICY IF EXISTS "Anyone can view templates" ON templates;
DROP POLICY IF EXISTS "Only admins can manage templates" ON templates;
DROP POLICY IF EXISTS "Anyone can view active templates" ON templates;
DROP POLICY IF EXISTS "Admins can view all templates" ON templates;
DROP POLICY IF EXISTS "Only admins can insert templates" ON templates;
DROP POLICY IF EXISTS "Only admins can update templates" ON templates;
DROP POLICY IF EXISTS "Only admins can delete templates" ON templates;

-- Create comprehensive admin policies for templates
CREATE POLICY "Public can view active templates" ON templates
  FOR SELECT
  USING (is_active = true);

CREATE POLICY "Admins have full access to templates" ON templates
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- =====================================================
-- 2. FIX VISITOR LOGS POLICIES
-- =====================================================

-- Drop existing visitor logs policies
DROP POLICY IF EXISTS "Anyone can insert visitor logs" ON visitor_logs;
DROP POLICY IF EXISTS "Only admins can view visitor logs" ON visitor_logs;
DROP POLICY IF EXISTS "Only admins can delete visitor logs" ON visitor_logs;

-- Create new visitor logs policies
CREATE POLICY "Anyone can insert visitor logs" ON visitor_logs
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Admins can manage visitor logs" ON visitor_logs
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- =====================================================
-- 3. FIX CONTACT REQUESTS POLICIES
-- =====================================================

-- Drop existing contact requests policies
DROP POLICY IF EXISTS "Anyone can insert contact requests" ON contact_requests;
DROP POLICY IF EXISTS "Only admins can view contact requests" ON contact_requests;
DROP POLICY IF EXISTS "Only admins can manage contact requests" ON contact_requests;

-- Create new contact requests policies
CREATE POLICY "Anyone can insert contact requests" ON contact_requests
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Users can view their own contact requests" ON contact_requests
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all contact requests" ON contact_requests
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- =====================================================
-- 4. FIX PURCHASES POLICIES
-- =====================================================

-- Drop existing purchases policies
DROP POLICY IF EXISTS "Users can view their own purchases" ON purchases;
DROP POLICY IF EXISTS "Only admins can view all purchases" ON purchases;
DROP POLICY IF EXISTS "Only admins can manage purchases" ON purchases;

-- Create new purchases policies
CREATE POLICY "Users can view their own purchases" ON purchases
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all purchases" ON purchases
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- =====================================================
-- 5. FIX CUSTOMIZATIONS POLICIES
-- =====================================================

-- Drop existing customizations policies
DROP POLICY IF EXISTS "Users can manage their own customizations" ON customizations;
DROP POLICY IF EXISTS "Only admins can view all customizations" ON customizations;

-- Create new customizations policies
CREATE POLICY "Users can manage their own customizations" ON customizations
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins can manage all customizations" ON customizations
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- =====================================================
-- 6. ENSURE RLS IS ENABLED ON ALL TABLES
-- =====================================================

ALTER TABLE templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE visitor_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE customizations ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 7. CREATE FUNCTION TO CLEAN OLD VISITOR LOGS (7 DAYS)
-- =====================================================

CREATE OR REPLACE FUNCTION clean_old_visitor_logs()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete visitor logs older than 7 days
  DELETE FROM visitor_logs 
  WHERE created_at < NOW() - INTERVAL '7 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins will use this)
GRANT EXECUTE ON FUNCTION clean_old_visitor_logs() TO authenticated;

-- =====================================================
-- 8. CREATE FUNCTION TO CLEAN OLD CONTACT REQUESTS (30 DAYS)
-- =====================================================

CREATE OR REPLACE FUNCTION clean_old_contact_requests()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete resolved contact requests older than 30 days
  DELETE FROM contact_requests 
  WHERE created_at < NOW() - INTERVAL '30 days'
  AND status IN ('resolved', 'closed');
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins will use this)
GRANT EXECUTE ON FUNCTION clean_old_contact_requests() TO authenticated;

-- =====================================================
-- 9. VERIFY ADMIN USER EXISTS
-- =====================================================

-- Check if admin user exists, if not, create one
DO $$
BEGIN
  -- This will help you verify if you have an admin user
  -- You should run this separately and check the results
  RAISE NOTICE 'Checking for admin users...';
  
  IF NOT EXISTS (
    SELECT 1 FROM profiles WHERE role = 'admin'
  ) THEN
    RAISE NOTICE 'No admin users found. Please create an admin user manually.';
  ELSE
    RAISE NOTICE 'Admin users found. Delete functionality should work now.';
  END IF;
END $$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

-- This script has:
-- ✅ Fixed RLS policies for all admin operations
-- ✅ Enabled proper delete functionality for admins
-- ✅ Created functions for bulk cleanup operations
-- ✅ Set visitor log cleanup to 7 days as requested
-- ✅ Ensured all tables have proper RLS enabled

-- NEXT STEPS:
-- 1. Run this script in Supabase SQL Editor
-- 2. Verify you have an admin user with role = 'admin' in profiles table
-- 3. Test delete functionality in admin panel
-- 4. Use the clean_old_visitor_logs() function for bulk cleanup
