-- Enhanced Templates Table Migration for Existing Database
-- Run this in Supabase SQL Editor
-- This will enhance your existing templates table with new fields

-- First, let's add all the new columns to the existing templates table
ALTER TABLE templates 
ADD COLUMN IF NOT EXISTS slug TEXT,
ADD COLUMN IF NOT EXISTS long_description TEXT,
ADD COLUMN IF NOT EXISTS original_price INTEGER,
ADD COLUMN IF NOT EXISTS discount_percentage INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS demo_url TEXT,
ADD COLUMN IF NOT EXISTS download_url TEXT,
ADD COLUMN IF NOT EXISTS version TEXT DEFAULT '1.0.0',
ADD COLUMN IF NOT EXISTS features TEXT[],
ADD COLUMN IF NOT EXISTS tech_stack TEXT[],
ADD COLUMN IF NOT EXISTS difficulty_level TEXT DEFAULT 'beginner',
ADD COLUMN IF NOT EXISTS estimated_time TEXT,
ADD COLUMN IF NOT EXISTS license_type TEXT DEFAULT 'standard',
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_free BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS downloads_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS views_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS rating DECIMAL(2,1) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS reviews_count INTEGER DEFAULT 0;

-- Add constraints for better data integrity (only if they don't exist)
DO $$
BEGIN
    -- Add check_price_positive constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_price_positive') THEN
        ALTER TABLE templates ADD CONSTRAINT check_price_positive CHECK (price >= 0);
    END IF;

    -- Add check_original_price_positive constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_original_price_positive') THEN
        ALTER TABLE templates ADD CONSTRAINT check_original_price_positive CHECK (original_price IS NULL OR original_price >= 0);
    END IF;

    -- Add check_discount_percentage constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_discount_percentage') THEN
        ALTER TABLE templates ADD CONSTRAINT check_discount_percentage CHECK (discount_percentage >= 0 AND discount_percentage <= 100);
    END IF;

    -- Add check_difficulty_level constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_difficulty_level') THEN
        ALTER TABLE templates ADD CONSTRAINT check_difficulty_level CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced'));
    END IF;

    -- Add check_license_type constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_license_type') THEN
        ALTER TABLE templates ADD CONSTRAINT check_license_type CHECK (license_type IN ('standard', 'extended', 'commercial'));
    END IF;

    -- Add check_rating constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_rating') THEN
        ALTER TABLE templates ADD CONSTRAINT check_rating CHECK (rating >= 0.0 AND rating <= 5.0);
    END IF;
END $$;

-- Create unique index on slug for SEO-friendly URLs
CREATE UNIQUE INDEX IF NOT EXISTS templates_slug_idx ON templates(slug) WHERE slug IS NOT NULL;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS templates_category_idx ON templates(category);
CREATE INDEX IF NOT EXISTS templates_is_featured_idx ON templates(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS templates_is_premium_idx ON templates(is_premium) WHERE is_premium = true;
CREATE INDEX IF NOT EXISTS templates_is_free_idx ON templates(is_free) WHERE is_free = true;
CREATE INDEX IF NOT EXISTS templates_downloads_count_idx ON templates(downloads_count DESC);
CREATE INDEX IF NOT EXISTS templates_views_count_idx ON templates(views_count DESC);
CREATE INDEX IF NOT EXISTS templates_rating_idx ON templates(rating DESC);
CREATE INDEX IF NOT EXISTS templates_created_at_idx ON templates(created_at DESC);

-- Update existing templates with enhanced data and add new sample templates
-- First, let's update your existing templates with some enhanced fields (only if slug is null)
UPDATE templates SET
  slug = LOWER(REGEXP_REPLACE(title, '[^a-zA-Z0-9]+', '-', 'g')),
  long_description = CASE
    WHEN title = 'Modern Business' THEN 'A comprehensive business template featuring modern design, responsive layout, and conversion-optimized sections. Perfect for startups and professional services.'
    WHEN title = 'Creative Portfolio' THEN 'Showcase your creative work with this beautiful portfolio template. Features smooth animations, project galleries, and contact integration.'
    WHEN title = 'E-commerce Store' THEN 'Complete e-commerce solution with product catalog, shopping cart, user authentication, and payment processing.'
    WHEN title = 'Landing Page Pro' THEN 'High-converting landing page template optimized for marketing campaigns and lead generation.'
    WHEN title = 'Blog & Magazine' THEN 'Perfect template for blogs, magazines, and content-focused websites with clean typography and reading experience.'
    WHEN title = 'Restaurant Menu' THEN 'Elegant restaurant template with menu showcase, online ordering, and table reservation system.'
    ELSE description
  END,
  original_price = CASE
    WHEN price >= 2000 THEN price + 1000
    WHEN price >= 1000 THEN price + 500
    ELSE price + 300
  END,
  features = CASE
    WHEN category = 'Business' THEN ARRAY['Responsive Design', 'SEO Optimized', 'Contact Forms', 'Modern UI']
    WHEN category = 'Portfolio' THEN ARRAY['Image Gallery', 'Smooth Animations', 'Project Showcase', 'Contact Integration']
    WHEN category = 'E-commerce' THEN ARRAY['Shopping Cart', 'Payment Integration', 'Product Catalog', 'User Authentication']
    WHEN category = 'Marketing' THEN ARRAY['Lead Generation', 'Conversion Optimized', 'Analytics Ready', 'A/B Testing']
    WHEN category = 'Blog' THEN ARRAY['Clean Typography', 'Reading Progress', 'Social Sharing', 'Comment System']
    WHEN category = 'Restaurant' THEN ARRAY['Menu Showcase', 'Online Ordering', 'Table Reservations', 'Food Gallery']
    ELSE ARRAY['Responsive Design', 'Modern UI', 'SEO Optimized']
  END,
  tech_stack = ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Supabase'],
  difficulty_level = CASE
    WHEN price >= 3000 THEN 'advanced'
    WHEN price >= 1500 THEN 'intermediate'
    ELSE 'beginner'
  END,
  estimated_time = CASE
    WHEN price >= 3000 THEN '4-6 hours'
    WHEN price >= 1500 THEN '2-3 hours'
    ELSE '1-2 hours'
  END,
  is_featured = CASE
    WHEN title IN ('Modern Business', 'Creative Portfolio', 'E-commerce Store') THEN true
    ELSE false
  END,
  is_premium = CASE
    WHEN price >= 2500 THEN true
    ELSE false
  END,
  downloads_count = FLOOR(RANDOM() * 200) + 50,
  views_count = FLOOR(RANDOM() * 1000) + 200,
  rating = ROUND((RANDOM() * 1.5 + 3.5)::numeric, 1),
  version = '1.0.0',
  license_type = 'standard',
  is_active = true
WHERE slug IS NULL OR long_description IS NULL;

-- Now let's add some additional sample templates with enhanced data (only if they don't exist)
INSERT INTO templates (
  title,
  description,
  long_description,
  price,
  original_price,
  discount_percentage,
  category,
  preview_image,
  preview_url,
  demo_url,
  slug,
  features,
  tech_stack,
  difficulty_level,
  estimated_time,
  license_type,
  is_featured,
  is_premium,
  downloads_count,
  views_count,
  rating
)
SELECT * FROM (VALUES
(
  'SaaS Dashboard Pro',
  'Professional dashboard template for SaaS applications',
  'Comprehensive SaaS dashboard with analytics, user management, billing, and settings. Includes charts, tables, forms, and responsive design. Perfect for building admin panels and user dashboards.',
  2499,
  3299,
  24,
  'Technology',
  'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop',
  'https://saas-dashboard-demo.vercel.app',
  'https://saas-dashboard-demo.vercel.app',
  'saas-dashboard-pro',
  ARRAY['Analytics Dashboard', 'User Management', 'Billing Integration', 'Charts & Graphs', 'Responsive Tables', 'Settings Panel'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Chart.js', 'Supabase', 'Stripe'],
  'advanced',
  '5-7 hours',
  'extended',
  true,
  true,
  98,
  480,
  4.8
),
(
  'Free Startup Landing',
  'Clean and minimal landing page for startups',
  'Beautiful startup landing page template with clean design, hero section, features showcase, and contact forms. Perfect for new businesses and product launches.',
  0,
  NULL,
  0,
  'Business',
  'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=600&fit=crop',
  'https://startup-landing-demo.vercel.app',
  'https://startup-landing-demo.vercel.app',
  'free-startup-landing',
  ARRAY['Clean Design', 'Hero Section', 'Features Showcase', 'Contact Forms', 'SEO Optimized', 'Mobile First'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
  'beginner',
  '1-2 hours',
  'standard',
  true,
  false,
  312,
  1580,
  4.5
),
(
  'Education Platform',
  'Complete education platform template with course management',
  'Comprehensive education template with course listings, student dashboard, instructor profiles, and learning management system. Perfect for online education platforms.',
  3499,
  4499,
  22,
  'Education',
  'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=600&fit=crop',
  'https://education-platform-demo.vercel.app',
  'https://education-platform-demo.vercel.app',
  'education-platform',
  ARRAY['Course Management', 'Student Dashboard', 'Instructor Profiles', 'Video Streaming', 'Progress Tracking', 'Certificates'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Supabase', 'Video.js', 'PDF Generation'],
  'advanced',
  '6-8 hours',
  'extended',
  true,
  true,
  67,
  340,
  4.7
)) AS new_templates(title, description, long_description, price, original_price, discount_percentage, category, preview_image, preview_url, demo_url, slug, features, tech_stack, difficulty_level, estimated_time, license_type, is_featured, is_premium, downloads_count, views_count, rating)
WHERE NOT EXISTS (
  SELECT 1 FROM templates WHERE templates.slug = new_templates.slug
);

-- Calculate discount percentages for all templates with original_price
UPDATE templates
SET discount_percentage = ROUND(((original_price - price)::numeric / original_price::numeric) * 100)
WHERE original_price IS NOT NULL AND original_price > price;

-- Create a function to automatically generate unique slug from title
CREATE OR REPLACE FUNCTION generate_slug_from_title()
RETURNS TRIGGER AS $$
DECLARE
  base_slug TEXT;
  final_slug TEXT;
  counter INTEGER := 1;
BEGIN
  IF NEW.slug IS NULL OR NEW.slug = '' THEN
    -- Generate base slug from title
    base_slug := LOWER(REGEXP_REPLACE(NEW.title, '[^a-zA-Z0-9]+', '-', 'g'));
    base_slug := TRIM(BOTH '-' FROM base_slug);
    final_slug := base_slug;

    -- Check for existing slug and make it unique if needed
    WHILE EXISTS (SELECT 1 FROM templates WHERE slug = final_slug AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)) LOOP
      final_slug := base_slug || '-' || counter;
      counter := counter + 1;
    END LOOP;

    NEW.slug := final_slug;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate slug
DROP TRIGGER IF EXISTS generate_slug_trigger ON templates;
CREATE TRIGGER generate_slug_trigger
  BEFORE INSERT OR UPDATE ON templates
  FOR EACH ROW
  EXECUTE FUNCTION generate_slug_from_title();

-- Create a function to calculate discount percentage automatically
CREATE OR REPLACE FUNCTION calculate_discount_percentage()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.original_price IS NOT NULL AND NEW.original_price > 0 AND NEW.price < NEW.original_price THEN
    NEW.discount_percentage := ROUND(((NEW.original_price - NEW.price)::numeric / NEW.original_price::numeric) * 100);
  ELSE
    NEW.discount_percentage := 0;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-calculate discount
DROP TRIGGER IF EXISTS calculate_discount_trigger ON templates;
CREATE TRIGGER calculate_discount_trigger
  BEFORE INSERT OR UPDATE ON templates
  FOR EACH ROW
  EXECUTE FUNCTION calculate_discount_percentage();

-- Update RLS Policies for Enhanced Templates Table
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can view templates" ON templates;
DROP POLICY IF EXISTS "Only admins can manage templates" ON templates;

-- Create updated policies for templates (public read, admin write)
CREATE POLICY "Anyone can view active templates" ON templates
  FOR SELECT
  USING (is_active = true);

CREATE POLICY "Admins can view all templates" ON templates
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Only admins can insert templates" ON templates
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Only admins can update templates" ON templates
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Only admins can delete templates" ON templates
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- Ensure RLS is enabled on templates table
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;

-- Create additional helpful policies for better security

-- Policy for anonymous users to view featured templates
CREATE POLICY "Anonymous can view featured templates" ON templates
  FOR SELECT
  USING (is_active = true AND is_featured = true);

-- Policy for anonymous users to view free templates
CREATE POLICY "Anonymous can view free templates" ON templates
  FOR SELECT
  USING (is_active = true AND is_free = true);

-- Create a view for public template access (optional, for better performance)
CREATE OR REPLACE VIEW public_templates AS
SELECT
  id, title, description, long_description, price, original_price,
  discount_percentage, category, preview_image, preview_url, demo_url,
  slug, features, tech_stack, difficulty_level, estimated_time,
  license_type, is_featured, is_premium, is_free, downloads_count,
  views_count, rating, reviews_count, created_at, updated_at
FROM templates
WHERE is_active = true;

-- Grant access to the public view
GRANT SELECT ON public_templates TO anon, authenticated;

-- Migration completed successfully!
-- Your enhanced template system is now ready with:
-- ✅ 20+ new fields added to templates table
-- ✅ Enhanced existing templates with rich data
-- ✅ 3 new sample templates added
-- ✅ Automatic slug generation and discount calculation
-- ✅ Performance indexes created
-- ✅ Proper RLS policies for security
-- ✅ Public view for better performance
