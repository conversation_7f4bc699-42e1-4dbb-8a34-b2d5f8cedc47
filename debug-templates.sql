-- Debug query to check template data
-- Run this in Supabase SQL Editor to see what's in your templates table

-- Check all templates and their featured/active status
SELECT 
  id,
  title,
  is_featured,
  is_active,
  created_at
FROM templates 
ORDER BY created_at DESC;

-- Check specifically for featured templates
SELECT 
  id,
  title,
  is_featured,
  is_active,
  created_at
FROM templates 
WHERE is_featured = true AND is_active = true;

-- Update some templates to be featured if none are featured
UPDATE templates 
SET is_featured = true, is_active = true 
WHERE title IN ('Modern Business', 'Creative Portfolio', 'E-commerce Store')
AND (is_featured IS NULL OR is_featured = false OR is_active IS NULL OR is_active = false);

-- Check the count of featured templates
SELECT COUNT(*) as featured_count 
FROM templates 
WHERE is_featured = true AND is_active = true;
