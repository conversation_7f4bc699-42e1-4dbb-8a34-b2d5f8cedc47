# 🗄️ Database Setup Guide

## 📋 **Step-by-Step Database Setup**

### **1. Choose Your Database Schema**

**✅ RECOMMENDED: Use `database-schema.sql`**
- This is the complete, comprehensive schema
- Includes all enhanced features and proper structure
- Best for new setups or complete rebuilds

**❌ NOT RECOMMENDED: `database-migration.sql`**
- Only for upgrading existing basic schemas
- May cause conflicts if run on empty database

### **2. Setup Process**

#### **Option A: Fresh Database Setup (RECOMMENDED)**

1. **Go to Supabase Dashboard**
   - Open your project in Supabase
   - Go to SQL Editor

2. **Run the Complete Schema**
   ```sql
   -- Copy and paste the entire content of database-schema.sql
   -- This will create all tables, policies, and functions
   ```

3. **Run the Admin Fix Script**
   ```sql
   -- Copy and paste the entire content of database-fix-admin-delete.sql
   -- This ensures proper delete functionality
   ```

#### **Option B: If You Have Existing Data**

1. **Backup Your Data First**
   ```sql
   -- Export your existing templates, contacts, etc.
   ```

2. **Run Migration Script**
   ```sql
   -- Only if you have existing basic schema
   -- Copy content from database-migration.sql
   ```

3. **Run the Admin Fix Script**
   ```sql
   -- Copy content from database-fix-admin-delete.sql
   ```

### **3. Verify Admin User**

After running the scripts, ensure you have an admin user:

```sql
-- Check if you have admin users
SELECT * FROM profiles WHERE role = 'admin';

-- If no admin users exist, create one:
-- First, sign up normally through your app
-- Then update the user role:
UPDATE profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

### **4. Test Delete Functionality**

1. **Login as Admin**
   - Go to `/admin` in your app
   - Login with your admin account

2. **Test Each Delete Function**
   - ✅ Delete a template
   - ✅ Delete a contact request  
   - ✅ Delete a visitor log
   - ✅ Delete a purchase record
   - ✅ Delete a customization
   - ✅ Use "Delete Logs Older Than 7 Days" button

### **5. Troubleshooting Delete Issues**

If delete still doesn't work:

1. **Check RLS Policies**
   ```sql
   -- View current policies
   SELECT * FROM pg_policies WHERE tablename IN ('templates', 'visitor_logs', 'contact_requests', 'purchases', 'customizations');
   ```

2. **Check Admin Role**
   ```sql
   -- Verify your user has admin role
   SELECT u.email, p.role 
   FROM auth.users u 
   JOIN profiles p ON u.id = p.id 
   WHERE u.id = auth.uid();
   ```

3. **Check Table Permissions**
   ```sql
   -- Ensure RLS is enabled
   SELECT tablename, rowsecurity 
   FROM pg_tables 
   WHERE tablename IN ('templates', 'visitor_logs', 'contact_requests', 'purchases', 'customizations');
   ```

### **6. Database Cleanup Schedule**

The system now automatically handles:

- **Visitor Logs**: Delete after 7 days (as requested)
- **Contact Requests**: Keep resolved ones for 30 days
- **Purchases**: Keep expired/refunded for 90 days
- **Customizations**: Keep drafts for 60 days

### **7. Files You Can Remove After Setup**

Once your database is working properly, you can remove:

- ✅ `database-diagnostic.sql` - Already removed
- ✅ `debug-templates.sql` - Already removed  
- ⚠️ `database-migration.sql` - Keep if you might need to migrate again
- ⚠️ `sample-data.sql` - Keep if you want to reset test data
- ✅ `database-fix-admin-delete.sql` - Can remove after running once

### **8. Final Verification**

After setup, verify everything works:

```bash
# Test your app
npm run dev

# Check admin panel
# Go to http://localhost:3000/admin
# Test all delete functions
# Verify "Delete Logs Older Than 7 Days" works
```

## 🎯 **Summary**

1. **Use `database-schema.sql` for complete setup**
2. **Run `database-fix-admin-delete.sql` to fix delete issues**
3. **Ensure you have an admin user with role = 'admin'**
4. **Test all delete functionality in admin panel**
5. **Visitor logs now delete after 7 days as requested**

Your database should now be properly configured with working delete functionality! 🚀
