import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { type } = await request.json()
    const supabase = createClient()

    // Check if user is admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    let result
    let message

    switch (type) {
      case 'visitor_logs':
        // Clean visitor logs older than 7 days
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
        
        const { error: visitorError, count: visitorCount } = await supabase
          .from('visitor_logs')
          .delete({ count: 'exact' })
          .lt('created_at', sevenDaysAgo.toISOString())

        if (visitorError) throw visitorError
        
        result = { deleted: visitorCount || 0 }
        message = `Deleted ${visitorCount || 0} visitor logs older than 7 days`
        break

      case 'contact_requests':
        // Clean resolved contact requests older than 30 days
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        
        const { error: contactError, count: contactCount } = await supabase
          .from('contact_requests')
          .delete({ count: 'exact' })
          .lt('created_at', thirtyDaysAgo.toISOString())
          .in('status', ['resolved', 'closed'])

        if (contactError) throw contactError
        
        result = { deleted: contactCount || 0 }
        message = `Deleted ${contactCount || 0} resolved contact requests older than 30 days`
        break

      case 'old_purchases':
        // Clean failed/cancelled purchases older than 90 days
        const ninetyDaysAgo = new Date()
        ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90)
        
        const { error: purchaseError, count: purchaseCount } = await supabase
          .from('purchases')
          .delete({ count: 'exact' })
          .lt('created_at', ninetyDaysAgo.toISOString())
          .in('status', ['expired', 'refunded'])

        if (purchaseError) throw purchaseError
        
        result = { deleted: purchaseCount || 0 }
        message = `Deleted ${purchaseCount || 0} old expired/refunded purchases`
        break

      case 'inactive_customizations':
        // Clean draft customizations older than 60 days
        const sixtyDaysAgo = new Date()
        sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60)
        
        const { error: customError, count: customCount } = await supabase
          .from('customizations')
          .delete({ count: 'exact' })
          .lt('created_at', sixtyDaysAgo.toISOString())
          .eq('status', 'draft')

        if (customError) throw customError
        
        result = { deleted: customCount || 0 }
        message = `Deleted ${customCount || 0} old draft customizations`
        break

      default:
        return NextResponse.json(
          { error: 'Invalid cleanup type' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message,
      result
    })

  } catch (error) {
    console.error('Cleanup error:', error)
    return NextResponse.json(
      { error: 'Failed to perform cleanup operation' },
      { status: 500 }
    )
  }
}

// GET endpoint to check cleanup statistics
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()

    // Check if user is admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get cleanup statistics
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const sixtyDaysAgo = new Date()
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60)
    
    const ninetyDaysAgo = new Date()
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90)

    // Count old records
    const [visitorLogs, contactRequests, oldPurchases, inactiveCustomizations] = await Promise.all([
      supabase
        .from('visitor_logs')
        .select('id', { count: 'exact', head: true })
        .lt('created_at', sevenDaysAgo.toISOString()),
      
      supabase
        .from('contact_requests')
        .select('id', { count: 'exact', head: true })
        .lt('created_at', thirtyDaysAgo.toISOString())
        .in('status', ['resolved', 'closed']),
      
      supabase
        .from('purchases')
        .select('id', { count: 'exact', head: true })
        .lt('created_at', ninetyDaysAgo.toISOString())
        .in('status', ['expired', 'refunded']),
      
      supabase
        .from('customizations')
        .select('id', { count: 'exact', head: true })
        .lt('created_at', sixtyDaysAgo.toISOString())
        .eq('status', 'draft')
    ])

    return NextResponse.json({
      success: true,
      statistics: {
        visitor_logs: {
          count: visitorLogs.count || 0,
          description: 'Visitor logs older than 7 days'
        },
        contact_requests: {
          count: contactRequests.count || 0,
          description: 'Resolved contact requests older than 30 days'
        },
        old_purchases: {
          count: oldPurchases.count || 0,
          description: 'Expired/refunded purchases older than 90 days'
        },
        inactive_customizations: {
          count: inactiveCustomizations.count || 0,
          description: 'Draft customizations older than 60 days'
        }
      }
    })

  } catch (error) {
    console.error('Statistics error:', error)
    return NextResponse.json(
      { error: 'Failed to get cleanup statistics' },
      { status: 500 }
    )
  }
}
